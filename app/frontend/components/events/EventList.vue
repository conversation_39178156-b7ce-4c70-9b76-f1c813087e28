<template>
  <div class="event-wrapper" data-vue-component="events-list">
    <div v-if="isLoading" class="event-loader">
      <div class="loader-spinner"></div>
    </div>   
    <div class="event-content">
      <MonthlyEventTable
        :class="{ 'is-loading': isLoading }"
        :events="filteredEvents"
        :works="works"
        :meetings="meetings"
        :holidays="holidays"
        :current-user-contract-id="currentUserContractId"
        @month-changed="fetchEvents"
        @event-deleted="handleEventDeleted"
        @work-deleted="handleWorkDeleted"
        @work-updated="handleWorkUpdated"
        @meeting-deleted="handleMeetingDeleted"
        @meeting-updated="handleMeetingUpdated"
        @show-add-event-form="showAddEventForm"
        @show-add-work-form="showAddWorkForm"
        v-cloak
      />
    </div>
    <div v-if="showEventForm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ $t('add_event', 'Přidat událost') }}</h3>
          <button @click="showEventForm = false" class="close-btn">&times;</button>
        </div>
        <div class="central-modal-content">
          <EventForm 
            @event-added="handleEventAdded" 
            :initial-date="selectedDate"
          />
        </div>
      </div>
    </div>
    <div v-if="showWorkForm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ $t('add_work', 'Přidat práci') }}</h3>
          <button @click="showWorkForm = false" class="close-btn">&times;</button>
        </div>
        <div class="central-modal-content">
          <WorkForm 
            @saved="handleWorkAdded" 
            @cancel="showWorkForm = false"
            :initial-date="selectedDate"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios';
  import { mapGetters } from 'vuex';
  import MonthlyEventTable from './MonthlyEventTable.vue';
  import EventForm from './EventForm.vue';
  import WorkForm from '../works/WorkForm.vue';
  import { sendFlashMessage } from '/utils/flashMessage';

  export default {
    components: {
      MonthlyEventTable,
      EventForm,
      WorkForm
    },  
    props: {
      initialEvents: {
        type: Array,
        default: () => []
      },
      initialWorks: {
        type: Array,
        default: () => []
      },
      initialMeetings: {
        type: Array,
        default: () => []
      },
      initialHolidays: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        isLoading: true,
        events: this.initialEvents,
        works: this.initialWorks,
        meetings: this.initialMeetings,
        holidays: this.initialHolidays,
        currentDate: new Date(),
        showEventForm: false,
        selectedDate: null,
        showWorkForm: false,
      };
    },
    computed: {
      ...mapGetters('calendarStore', ['currentUserContractId']),

      // Filter events to hide rejected events from non-owners
      filteredEvents() {
        return this.events.filter(event => {
          // Status filtering for rejected events
          if (event.status === 'rejected') {
            // Only show rejected events to the original event owner
            const isEventOwner = event.contract && event.contract.id === this.currentUserContractId;
            return isEventOwner; // Only the event creator can see their rejected events
          }

          return true; // Show all other events (pending, approved, etc.)
        });
      },

      groupedEvents() {
        return this.filteredEvents.reduce((groups, event) => {
          const date = new Date(event.start_time).toISOString().split('T')[0];
          if (!groups[date]) {
            groups[date] = [];
          }
          groups[date].push(event);
          return groups;
        }, {});
      },
    },
    async mounted() {
      // Always fetch events in SPA mode
      await this.fetchEvents(this.currentDate);
    },
    methods: {
      handleEventDeleted(eventId) {
        this.events = this.events.filter(event => event.id !== eventId);
      },
      
      handleWorkDeleted(workId) {
        this.works = this.works.filter(work => work.id !== workId);
        sendFlashMessage(this.$t('works.deleted', 'Pracovní položka byla smazána'), 'success');
      },
      
      handleWorkUpdated(updatedWork) {
        const index = this.works.findIndex(work => work.id === updatedWork.id);
        if (index !== -1) {
          this.works.splice(index, 1, updatedWork);
          sendFlashMessage(this.$t('works.updated', 'Pracovní položka byla aktualizována'), 'success');
        }
      },
      
      handleMeetingDeleted(meetingId) {
        this.meetings = this.meetings.filter(meeting => meeting.id !== meetingId);
        sendFlashMessage(this.$t('meetings.deleted', 'Schůzka byla smazána'), 'success');
      },
      
      handleMeetingUpdated(updatedMeeting) {
        const index = this.meetings.findIndex(meeting => meeting.id === updatedMeeting.id);
        if (index !== -1) {
          this.meetings.splice(index, 1, updatedMeeting);
          sendFlashMessage(this.$t('meetings.updated', 'Schůzka byla aktualizována'), 'success');
        }
      },
      
      async fetchEvents(date) {
        this.isLoading = true;
        try {
          const formattedMonth = date.toISOString().split('T')[0];
          
          // Always use API endpoint in SPA mode
          const response = await axios.get('/api/v1/events/fetch', {
            params: { date: formattedMonth },
            headers: { 'Accept': 'application/json' }
          });
          this.events = response.data.events || [];
          this.works = response.data.works || [];
          this.meetings = response.data.meetings || [];
          this.holidays = response.data.holidays || [];
        } catch (error) {
          console.error('Error fetching events:', error);
          sendFlashMessage(this.$t('events.fetch_error', 'Nepodařilo se načíst události'), 'error');
        } finally {
          this.isLoading = false;
        } 
      },
      
      async addEvent(event) {
        try {
          // Always use API endpoint in SPA mode
          const response = await axios.post('/api/v1/events', event, {
            headers: { 'Accept': 'application/json' }
          });
          if (response.data.event) {
            this.events.push(response.data.event);
          } else {
            this.events.push(response.data);
          }
        } catch (error) {
          console.error('Error adding event:', error);
        }
      },
      
      showAddEventForm(date, event) {
        this.selectedDate = date;
        this.showEventForm = true;
      },

      handleEventAdded(event) {
        this.events.push(event);
        this.showEventForm = false;
      },
      
      showAddWorkForm(date) {
        if (date instanceof Date) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          this.selectedDate = `${year}-${month}-${day}`;
        } else {
          this.selectedDate = date;
        }
        this.showWorkForm = true;
      },

      handleWorkAdded(work) {
        this.works.push(work);
        this.showWorkForm = false;
        sendFlashMessage(this.$t('works.created', 'Práce byla vytvořena'), 'success');
      },
    }
  };
</script>

<style scoped>
.event-wrapper {
  position: relative;
}

.event-content {
  min-height: 200px;
}

.event-loader {
  position: fixed;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  padding: 8px;
  background: white;
}

.loader-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.is-loading {
  opacity: 0.5;
  pointer-events: none;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

[v-cloak] {
  display: none;
}
</style>