<!-- ABOUTME: Weekly calendar view with responsive design for mobile and desktop -->
<!-- ABOUTME: Mobile: horizontal scroll, tap interactions; Desktop: full week, drag-drop -->
<template>
  <div class="weekly-calendar">
    <!-- Week navigation header -->
    <div class="week-header">
      <button @click="prevWeek" class="nav-btn">
        <ChevronLeft :size="20" />
      </button>
      <h2 class="week-title">{{ weekRangeText }}</h2>
      <button @click="nextWeek" class="nav-btn">
        <ChevronRight :size="20" />
      </button>
      <RefreshButton target="calendar" />
    </div>

    <!-- Week grid container -->
    <div class="week-grid-container">
      <div class="week-grid">
        <div v-for="(day, index) in weekDays" 
             :key="index"
             :class="['day-column', {
               'today': isToday(day.date),
               'weekend': isWeekend(day.date),
               'holiday': isHoliday(day.date)
             }]">
          
          <!-- Day header -->
          <div class="day-header">
            <span class="day-name">{{ formatDayName(day.date) }}</span>
            <span class="day-number">{{ day.date.getDate() }}</span>
          </div>
          
          <!-- Day content - mobile tap only, desktop drag enabled -->
          <div class="day-content" :data-date="formatDateKey(day.date)">
            <!-- On mobile: simple list, on desktop: draggable -->
            <draggable
              v-if="isDragEnabled"
              v-model="itemsByDate[formatDateKey(day.date)]"
              :group="{ name: 'calendar-items', pull: true, put: true }"
              item-key="uniqueId"
              class="items-container"
              @end="onDragEnd"
            >
              <template #item="{ element }">
                <div @click="showItemDetail(element)" class="item-wrapper">
                  <CalendarEventItem 
                    v-if="element.itemType === 'event'"
                    :event="element"
                    :is-draggable="true"
                    :is-compact="true"
                  />
                  <CalendarWorkItem 
                    v-else-if="element.itemType === 'work'"
                    :work="element"
                    :is-draggable="true"
                    :is-compact="true"
                  />
                </div>
              </template>
            </draggable>
            
            <!-- Mobile: non-draggable list -->
            <div v-else class="items-container">
              <div v-for="item in getItemsForDate(day.date)" 
                   :key="`${item.itemType}-${item.id}`"
                   @click="showItemDetail(item)"
                   class="item-wrapper mobile-tap">
                <CalendarEventItem 
                  v-if="item.itemType === 'event'"
                  :event="item"
                  :is-draggable="false"
                  :is-compact="true"
                />
                <CalendarWorkItem 
                  v-else-if="item.itemType === 'work'"
                  :work="item"
                  :is-draggable="false"
                  :is-compact="true"
                />
              </div>
            </div>
            
            <!-- Add buttons -->
            <div class="add-buttons">
              <button @click="showAddEvent(day.date)" class="add-btn" :title="$t('add_event', 'Přidat událost')">
                <CalendarPlus :size="14" />
              </button>
              <button @click="showAddWork(day.date)" class="add-btn" :title="$t('add_work', 'Přidat práci')">
                <FolderPlus :size="14" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile: Bottom Sheet for item details -->
    <div v-if="selectedItem && isMobileView" 
         class="mobile-detail-sheet" 
         :class="{ 'open': selectedItem }"
         @click.self="closeDetail">
      <div class="sheet-content">
        <div class="sheet-header">
          <h3>{{ getItemTitle(selectedItem) }}</h3>
          <button @click="closeDetail" class="close-btn">×</button>
        </div>
        <div class="sheet-body">
          <!-- Event details -->
          <template v-if="selectedItem.itemType === 'event'">
            <p><strong>{{ $t('time', 'Čas') }}:</strong> {{ formatEventTime(selectedItem) }}</p>
            <p v-if="selectedItem.description">{{ selectedItem.description }}</p>
          </template>
          
          <!-- Work details -->
          <template v-else-if="selectedItem.itemType === 'work'">
            <p><strong>{{ $t('company', 'Firma') }}:</strong> {{ selectedItem.company_name }}</p>
            <p><strong>{{ $t('status', 'Stav') }}:</strong> {{ getWorkStatusText(selectedItem.status) }}</p>
            <p v-if="selectedItem.scheduled_duration">
              <strong>{{ $t('duration', 'Trvání') }}:</strong> {{ selectedItem.scheduled_duration }} min
            </p>
          </template>
        </div>
        <div class="sheet-actions">
          <button @click="handleReschedule" class="btn btn-primary">
            {{ $t('reschedule', 'Přeplánovat') }}
          </button>
          <button @click="handleEdit" class="btn btn-outline">
            {{ $t('edit', 'Upravit') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Desktop: Use existing sidebar -->
    <div v-if="selectedItem && !isMobileView" class="desktop-detail">
      <!-- This would integrate with CalendarSidebar component -->
    </div>
  </div>
</template>

<script>
import { ChevronLeft, ChevronRight, CalendarPlus, FolderPlus } from 'lucide-vue-next';
import { mapState, mapGetters, mapActions } from 'vuex';
import draggable from 'vuedraggable';
import CalendarEventItem from '../calendar/CalendarEventItem.vue';
import CalendarWorkItem from '../calendar/CalendarWorkItem.vue';
import RefreshButton from '../shared/RefreshButton.vue';

export default {
  name: 'WeeklyCalendar',
  components: {
    ChevronLeft,
    ChevronRight,
    CalendarPlus,
    FolderPlus,
    CalendarEventItem,
    CalendarWorkItem,
    draggable,
    RefreshButton,
  },
  props: {
    holidays: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      currentWeekStart: null,
      selectedItem: null,
      itemsByDate: {},
      isMobileView: window.innerWidth < 768,
      isDragEnabled: window.innerWidth >= 1024,
    };
  },
  created() {
    this.initializeWeek();
    this.fetchCalendarData(new Date());
    
    // Add resize listener
    window.addEventListener('resize', this.handleResize);
  },
  unmounted() {
    window.removeEventListener('resize', this.handleResize);
  },
  computed: {
    ...mapState('calendarStore', ['works', 'loadingStates']),
    ...mapGetters('calendarStore', ['filteredEvents']),

    // Use filtered events instead of direct state access
    events() {
      return this.filteredEvents;
    },
    
    weekDays() {
      const days = [];
      const start = new Date(this.currentWeekStart);
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(start);
        date.setDate(start.getDate() + i);
        days.push({ date });
      }
      
      return days;
    },
    
    weekRangeText() {
      const start = this.weekDays[0].date;
      const end = this.weekDays[6].date;
      const monthFormatter = new Intl.DateTimeFormat(this.$i18n.locale, { month: 'long' });
      
      if (start.getMonth() === end.getMonth()) {
        return `${start.getDate()}-${end.getDate()} ${monthFormatter.format(start)}`;
      } else {
        const shortMonthFormatter = new Intl.DateTimeFormat(this.$i18n.locale, { month: 'short' });
        return `${start.getDate()} ${shortMonthFormatter.format(start)} - ${end.getDate()} ${shortMonthFormatter.format(end)}`;
      }
    }
  },
  watch: {
    events: {
      handler() {
        this.updateItemsByDate();
      },
      deep: true
    },
    works: {
      handler() {
        this.updateItemsByDate();
      },
      deep: true
    }
  },
  methods: {
    ...mapActions('calendarStore', ['fetchCalendarData', 'updateEventDate', 'updateWorkDate']),
    
    handleResize() {
      this.isMobileView = window.innerWidth < 768;
      this.isDragEnabled = window.innerWidth >= 1024;
    },
    
    initializeWeek() {
      const today = new Date();
      const dayOfWeek = today.getDay();
      const monday = new Date(today);
      // Adjust to get Monday as start of week
      monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
      monday.setHours(0, 0, 0, 0);
      this.currentWeekStart = monday;
    },
    
    prevWeek() {
      const newStart = new Date(this.currentWeekStart);
      newStart.setDate(newStart.getDate() - 7);
      this.currentWeekStart = newStart;
      this.fetchCalendarData(newStart);
    },
    
    nextWeek() {
      const newStart = new Date(this.currentWeekStart);
      newStart.setDate(newStart.getDate() + 7);
      this.currentWeekStart = newStart;
      this.fetchCalendarData(newStart);
    },
    
    isToday(date) {
      const today = new Date();
      return date.toDateString() === today.toDateString();
    },
    
    isWeekend(date) {
      const day = date.getDay();
      return day === 0 || day === 6;
    },
    
    isHoliday(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.holidays.includes(dateString);
    },
    
    formatDayName(date) {
      return date.toLocaleDateString(this.$i18n.locale, { weekday: 'short' });
    },
    
    formatDateKey(date) {
      return date.toLocaleDateString('en-CA'); // YYYY-MM-DD
    },
    
    updateItemsByDate() {
      const newItemsByDate = {};
      
      // Initialize all week days
      this.weekDays.forEach(day => {
        const dateKey = this.formatDateKey(day.date);
        newItemsByDate[dateKey] = [];
      });
      
      // Add events
      if (this.events) {
        this.events.forEach(event => {
          const eventDates = this.getEventDates(event);
          eventDates.forEach(dateKey => {
            if (newItemsByDate[dateKey]) {
              newItemsByDate[dateKey].push({
                ...event,
                itemType: 'event',
                uniqueId: `event-${event.id}`
              });
            }
          });
        });
      }
      
      // Add works
      if (this.works) {
        this.works.forEach(work => {
          const workDates = this.getWorkDates(work);
          workDates.forEach(dateKey => {
            if (newItemsByDate[dateKey]) {
              newItemsByDate[dateKey].push({
                ...work,
                itemType: 'work',
                uniqueId: `work-${work.id}`
              });
            }
          });
        });
      }
      
      this.itemsByDate = newItemsByDate;
    },
    
    getEventDates(event) {
      const dates = [];
      const start = new Date(event.start_time);
      const end = new Date(event.end_time);
      
      const current = new Date(start);
      while (current <= end) {
        dates.push(this.formatDateKey(current));
        current.setDate(current.getDate() + 1);
      }
      
      return dates;
    },
    
    getWorkDates(work) {
      const dates = [];
      const start = new Date(work.scheduled_start_date);
      const end = work.scheduled_end_date ? new Date(work.scheduled_end_date) : start;
      
      const current = new Date(start);
      while (current <= end) {
        dates.push(this.formatDateKey(current));
        current.setDate(current.getDate() + 1);
      }
      
      return dates;
    },
    
    getItemsForDate(date) {
      const dateKey = this.formatDateKey(date);
      return this.itemsByDate[dateKey] || [];
    },
    
    showItemDetail(item) {
      console.log('📅 Weekly: Showing item detail', item);
      this.selectedItem = item;
      
      if (!this.isMobileView) {
        // Dispatch to store for sidebar display
        this.$store.dispatch('calendarStore/showItemDetail', { 
          item, 
          itemType: item.itemType 
        });
      }
    },
    
    closeDetail() {
      this.selectedItem = null;
    },
    
    getItemTitle(item) {
      if (item.itemType === 'event') {
        return item.name || item.title || 'Event';
      } else if (item.itemType === 'work') {
        return item.title || 'Work';
      }
      return 'Item';
    },
    
    formatEventTime(event) {
      const start = new Date(event.start_time);
      const end = new Date(event.end_time);
      const timeFormat = { hour: '2-digit', minute: '2-digit' };
      return `${start.toLocaleTimeString(this.$i18n.locale, timeFormat)} - ${end.toLocaleTimeString(this.$i18n.locale, timeFormat)}`;
    },
    
    getWorkStatusText(status) {
      const statusMap = {
        'scheduled': this.$t('scheduled', 'Naplánováno'),
        'in_progress': this.$t('in_progress', 'Probíhá'),
        'completed': this.$t('completed', 'Dokončeno'),
        'cancelled': this.$t('cancelled', 'Zrušeno')
      };
      return statusMap[status] || status;
    },
    
    onDragEnd(event) {
      const toDate = event.to?.dataset?.date;
      const fromDate = event.from?.dataset?.date;
      
      if (!toDate || !fromDate || toDate === fromDate) {
        return;
      }
      
      // Get the dragged item
      const draggedItem = this.itemsByDate[toDate]?.[event.newIndex];
      
      if (draggedItem) {
        this.persistDragOperation(draggedItem, fromDate, toDate);
      }
    },
    
    async persistDragOperation(item, fromDate, toDate) {
      try {
        if (item.itemType === 'event') {
          await this.updateEventDate({
            eventId: item.id,
            newDate: toDate
          });
        } else if (item.itemType === 'work') {
          await this.updateWorkDate({
            workId: item.id,
            newDate: toDate
          });
        }
        
        await this.fetchCalendarData(this.currentWeekStart);
      } catch (error) {
        console.error('Failed to update item:', error);
        this.fetchCalendarData(this.currentWeekStart);
      }
    },
    
    handleReschedule() {
      // Emit event or show reschedule modal
      this.$emit('reschedule-item', this.selectedItem);
      this.closeDetail();
    },
    
    handleEdit() {
      // Emit event or navigate to edit
      this.$emit('edit-item', this.selectedItem);
      this.closeDetail();
    },
    
    showAddEvent(date) {
      this.$emit('show-add-event-form', date);
    },
    
    showAddWork(date) {
      this.$emit('show-add-work-form', date);
    }
  }
};
</script>

<style scoped>
.weekly-calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

/* Header */
.week-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #dee2e6;
  gap: 1rem;
}

.nav-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 9999px;
  color: #3490dc;
  transition: background-color 0.2s;
}

.nav-btn:hover {
  background-color: #f1f5f9;
}

.week-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  text-transform: capitalize;
}

/* Week grid */
.week-grid-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.week-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #dee2e6;
  height: 100%;
  min-height: 0;
}

.day-column {
  background: white;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.day-column.today {
  background-color: #e3f2fd;
}

.day-column.weekend {
  background-color: #f5f7fa;
}

.day-column.holiday {
  background-color: #e8f5e9;
}

/* Day header */
.day-header {
  padding: 0.75rem;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
}

.day-name {
  display: block;
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
}

.day-number {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
}

.day-column.today .day-number {
  color: #1976d2;
}

/* Day content */
.day-content {
  flex: 1;
  padding: 0.5rem;
  overflow-y: auto;
  min-height: 0;
}

.items-container {
  min-height: 2rem;
}

.item-wrapper {
  margin-bottom: 0.25rem;
  cursor: pointer;
  transition: transform 0.1s;
}

.item-wrapper:hover {
  transform: translateX(2px);
}

.item-wrapper.mobile-tap:active {
  transform: scale(0.98);
}

/* Add buttons */
.add-buttons {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.add-btn {
  flex: 1;
  padding: 0.25rem;
  background: none;
  border: 1px dashed #dee2e6;
  border-radius: 9999px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s;
}

.add-btn:hover {
  border-color: #22C55E;
  color: #22C55E;
}

/* Mobile bottom sheet */
.mobile-detail-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  background: rgba(0, 0, 0, 0);
  z-index: 1000;
  pointer-events: none;
  transition: background-color 0.3s;
}

.mobile-detail-sheet.open {
  background: rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

.sheet-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  max-height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mobile-detail-sheet.open .sheet-content {
  transform: translateY(0);
}

.sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.sheet-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  line-height: 1;
  color: #6c757d;
}

.sheet-body {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
}

.sheet-body p {
  margin: 0.5rem 0;
}

.sheet-actions {
  padding: 1rem;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 0.5rem;
}

.sheet-actions .btn {
  flex: 1;
}

/* Drag and drop styles */
.sortable-ghost {
  opacity: 0.4;
}

.sortable-drag {
  opacity: 0;
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .week-grid {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
    display: flex;
  }
  
  .day-column {
    min-width: 140px;
    flex: 0 0 140px;
    scroll-snap-align: start;
  }
  
  .day-content {
    padding: 0.25rem;
  }
  
  .day-header {
    padding: 0.5rem;
  }
  
  .week-title {
    font-size: 1rem;
  }
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .week-grid {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .day-column {
    min-width: 120px;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .day-content {
    cursor: default;
  }
  
  .item-wrapper {
    cursor: move;
  }
  
  /* Hide mobile detail sheet on desktop */
  .mobile-detail-sheet {
    display: none;
  }
}
</style>