<!-- ContractForm.vue -->
<template>
  <div class="content-panel">
    <div v-if="errors.length" class="form-error-container mb-4">
      <div v-for="(error, index) in errors" :key="index" class="form-error-item">
        {{ error }}
      </div>
    </div>
    
    <form @submit.prevent="submitForm" >
      <div class="form-group">
        <label for="contractFirstName" class="form-label">{{ $t('first_name', 'Jmé<PERSON>') }}</label>
        <input v-model="form.first_name" 
               id="contractFirstName"
               type="text" 
               class="form-input" 
               :placeholder="$t('first_name', '<PERSON>méno')" 
               autocorrect="off" 
               autocapitalize="off">
      </div>

      <div class="form-group">
        <label for="contractLastName" class="form-label">{{ $t('last_name', 'Příjmení') }}</label>
        <input v-model="form.last_name" 
               id="contractLastName"
               type="text" 
               class="form-input" 
               :placeholder="$t('last_name', 'Příjmen<PERSON>')" 
               autocorrect="off" 
               autocapitalize="off">
      </div>

      <div class="form-group">
        <label for="contractJobTitle" class="form-label">{{ $t('job_title', 'Pracovní titul') }}</label>
        <input v-model="form.job_title" 
               id="contractJobTitle"
               type="text" 
               class="form-input" 
               :placeholder="$t('job_title', 'Pracovní titul')" 
               autocorrect="off" 
               autocapitalize="off">
      </div>

      <div class="form-group">
        <label for="contractType" class="form-label">{{ $t('contracts.contract_type_label', 'Typ pracovního poměru') }}</label>
        <input v-model="form.contract_type" 
               id="contractType"
               type="text" 
               class="form-input" 
               :placeholder="$t('contracts.contract_type_placeholder', 'Typ pracovního poměru')" 
               autocorrect="off" 
               autocapitalize="off">
      </div>

      <div class="form-group">
        <label for="contractPhone" class="form-label">{{ $t('phone', 'Telefon') }}</label>
        <input v-model="form.phone" 
               id="contractPhone"
               type="text" 
               class="form-input" 
               :placeholder="$t('phone', 'Telefon')" 
               autocorrect="off" 
               autocapitalize="off">
      </div>
      
      <div v-if="isNewRecord" class="form-section">
        <h4 class="text-lg font-semibold mb-2">{{ $t('contracts.email_invitation_title', 'Pozvání na e-mail') }}</h4>
        <p class="text-sm text-gray-600 mb-1">{{ $t('contracts.email_invitation_desc_1', 'Na uvedený e-mail odešleme pozvánku na připojení sa k Vašemu pracovnímu prostoru.') }}</p>
        <p class="text-sm text-gray-600 mb-3">{{ $t('contracts.email_invitation_desc_2', 'Když chcete přidat sami sebe, ponechte prázdné.') }}</p>
        <div class="form-group">
          <label for="contractEmail" class="form-label">{{ $t('email', 'E-mail') }}</label>
          <input v-model="form.email" 
                 id="contractEmail"
                 type="email" 
                 class="form-input" 
                 :placeholder="$t('email', 'E-mail')" 
                 autocorrect="off" 
                 autocapitalize="off">
        </div>
      </div>

      <div class="flex justify-end gap-3 mt-6">
        <button type="button" @click="$emit('cancel')" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
        <button type="submit" class="btn btn-primary">{{ $t('save', 'Uložit') }}</button>
      </div>
    </form>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  props: {
    contractId: {
      type: [Number, String],
      default: null
    },
    prefillEmail: {
      type: String,
      default: null
    },
    prefillFirstName: {
      type: String,
      default: null
    },
    prefillLastName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      form: {
        first_name: '',
        last_name: '',
        job_title: '',
        contract_type: '',
        phone: '',
        email: ''
      },
      errors: [],
      loading: false
    }
  },
  computed: {
    isNewRecord() {
      return !this.contractId
    }
  },
  mounted() {
    if (this.contractId) {
      this.fetchContract()
    } else if (this.prefillEmail) {
      // Handle reinvitation - prefill the form with terminated contract data
      this.form.email = this.prefillEmail
      this.form.first_name = this.prefillFirstName || ''
      this.form.last_name = this.prefillLastName || ''
    }
  },
  methods: {
    fetchContract() {
      this.loading = true
      
      axios.get(`/contracts/${this.contractId}/edit`, {
        headers: { 'Accept': 'application/json' }
      })
      .then(response => {
        const data = response.data
        this.form = {
          first_name: data.first_name || '',
          last_name: data.last_name || '',
          job_title: data.job_title || '',
          contract_type: data.contract_type || '',
          phone: data.phone || '',
          email: data.email || ''
        }
        this.loading = false
      })
      .catch(error => {
        console.error('Error:', error)
        this.errors.push(this.$t('contracts.error_loading_data', 'Nepodařilo se načíst data'))
        this.loading = false
      })
    },
    submitForm() {
      this.errors = []
      this.loading = true
      
      const url = this.isNewRecord 
                  ? '/contracts' 
                  : `/contracts/${this.contractId}`
                  
      const method = this.isNewRecord ? 'post' : 'patch'
      
      axios({
        method,
        url,
        data: { contract: this.form },
        headers: { 'Accept': 'application/json' }
      })
      .then(response => {
        this.loading = false
        this.$emit('saved', response.data)
        this.$emit('message', { 
          type: 'success', 
          text: this.isNewRecord 
                ? this.$t('contracts.contract_created_message', 'Kontrakt byl vytvořen') 
                : this.$t('contracts.contract_updated_message', 'Kontrakt byl aktualizován')
        })
      })
      .catch(error => {
        this.loading = false
        console.error('Error:', error)
        if (error.response && error.response.data.errors) {
          this.errors = error.response.data.errors
        } else {
          this.errors.push(this.$t('contracts.error_saving_data', 'Došlo k chybě při ukládání'))
        }
      })
    }
  }
}
</script>