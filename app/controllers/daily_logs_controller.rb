class DailyLogsController < ApplicationController
  include HolidayFetcher
  
  # Authentication handled by ApplicationController's require_login
  before_action :set_tenant_company
  before_action :set_contract

  def index
    # Generates index file with vue component only
    # if @contract.nil?
    #   redirect_to root_path, alert: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kolegy." and return
    # end
    # @daily_logs = @contract.daily_logs.order(created_at: :desc)
    @contract_debug = @contract
  end

  # Vuejs endpoint: Create a new daily log. Using contract.daily_logs to secure the scope - only the current users logs can be created
  def create
    handle_previous_open_logs
    today = Time.current.to_date
    start_time = Time.current
    day_range = DateTimeHelper.day_range(today)
    daily_log = @contract.daily_logs.find_by(start_time: day_range)

    unless daily_log
      daily_log = @contract.daily_logs.create(start_time: start_time, user: current_user)
    end

    if daily_log.end_time.present?
      daily_log.reopen
      @notifications << {
        type: 'info',
        message: daily_logs_t('messages.continuing_todays_log')
      }
    end
    
    if daily_log.valid?
      @notifications = []
      @notifications << daily_log.time_warning if daily_log.time_warning.present?
      
      # Broadcast team status update when daily log is started
      broadcast_team_status_update(
        employee: current_user,
        daily_log: daily_log,
        event_type: 'daily_log_started'
      )
      
      render json: { daily_log: daily_log, notifications: @notifications }
    else
      render json: { errors: daily_log.errors.full_messages }, status: :unprocessable_entity
    end

  end

  # Vuejs endpoint: Update a daily log. Using contract.daily_logs to secure the scope - only the current users logs can be updated
  # Keep update only for system actions like end_work, not for activity descriptions
  def update
    @daily_log = @contract.daily_logs.find(params[:id])
  
    if @daily_log.update(daily_log_params)
      # If ending the daily log, also end all active activities
      if daily_log_params[:end_time].present?
        DailyActivityManager::AutoCloser.call(daily_log: @daily_log)
        
        # Broadcast team status update when daily log is ended
        broadcast_team_status_update(
          employee: current_user,
          daily_log: @daily_log,
          event_type: 'daily_log_ended'
        )
      end
      
      notifications = []
      notifications << @daily_log.time_warning if @daily_log.time_warning.present?
      render json: { daily_log: @daily_log, notifications: notifications }
    else
      render json: { errors: @daily_log.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # Vuejs endpoint: Delete a daily log. Using contract.daily_logs to secure the scope - only the current users logs can be deleted
  def destroy
    @daily_log = @contract.daily_logs.find(params[:id])
    if @daily_log.destroy
      render json: { message: daily_logs_t('messages.deleted') }, status: :ok
    else
      render json: { errors: @daily_log.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # Vuejs endpoint: Return all daily logs as JSON for Vue component
  # After Timezone refactor
  def fetch
    start_of_day = DateTimeHelper.beginning_of_day(Date.parse(params[:date]))
    end_of_day = DateTimeHelper.end_of_day(start_of_day)

    daily_logs = @contract.daily_logs.where(start_time: start_of_day..end_of_day).order(created_at: :desc)
    render json: daily_logs, status: :ok
  end

  # Vuejs endpoint: Return the last log for the current users contract
  def last
    @last_log = @contract.daily_logs.last
    if @last_log
      render json: @last_log
    else
      render json: { error: 'No logs found' }, status: :not_found
    end
  end

  def report
  end

  def fetch_report_data
    date_param = safe_parse_date(params[:date]) || Date.current
    
    if date_param
      report_data = ReportDataService.generate_report_data(@contract, date_param)
      render json: report_data
    else
      render json: { error: 'Invalid date format' }, status: :unprocessable_entity
    end
  end

  def fetch_day_data
    date = safe_parse_date(params[:date])
    
    if date
      log = @contract.daily_logs
        .where(start_time: date.beginning_of_day..date.end_of_day)
        .includes(:breaks)  # Add this
        .select('id, start_time, end_time, duration')
        .first

      if log 
        break_duration = log.breaks.first&.duration || 0
        net_duration = [(log.duration || 0) - break_duration, 0].max

  
      render json: {
        log: log.as_json(include: {
          breaks: { only: [:id, :start_time, :end_time] }
        }).merge(duration: net_duration)
      }
      else
        render json: { log: nil }
      end
    else
      render json: { error: 'Invalid date format' }, status: :unprocessable_entity
    end
  end
  
  ######## Last version before updating to daily_activities above


  # Standalone create for editing report - many implications later: 
  # Automation could be set in the Settings and handled here
  # TODO: Automate editing with automatically set start_time or end_time
  def create_for_report
    date_string = params.dig(:daily_log, :date)
  
    if date_string.present? && date_string =~ /^\d{4}-\d{2}-\d{2}$/
      begin
        date = Date.parse(date_string)
      rescue ArgumentError
        return render json: { error: daily_logs_t('errors.invalid_date_format') }
      end
    else
      return render json: { error: daily_logs_t('errors.date_required') }
    end

    @daily_log = @contract.daily_logs.build(daily_log_params.except(:date))
    @daily_log.user = current_user
    @daily_log.is_report_creation = true
    
    # Set default times based on parsed date
    zone = Time.zone
    user_settings = current_user.user_setting
    default_times = if user_settings.start_time.present? && user_settings.end_time.present?
      [user_settings.start_time, user_settings.end_time]
    else
      [Time.new(2000, 1, 1, 8, 0), Time.new(2000, 1, 1, 16, 30)]
    end

    @daily_log.start_time = date.in_time_zone(zone).change(hour: default_times[0].hour, min: default_times[0].min)
    @daily_log.end_time = date.in_time_zone(zone).change(hour: default_times[1].hour, min: default_times[1].min)

    if @daily_log.save
      render json: @daily_log, status: :created
    else
      render json: { errors: @daily_log.errors.messages.values.flatten.join(" ") }
    end
  end

  # TODO: reconsider what to do about it further
  # After Timezone refactor
  def current_status
    current_date = Date.current
    day_range = DateTimeHelper.day_range(current_date)

    # Preload all needed associations in single queries
    last_log = current_user.daily_logs
      #.includes(:breaks) # Include breaks to avoid n+1
      .where(contract: @contract)
      .where(start_time: day_range)
      .order(created_at: :desc)
      .first
      
    today_event = current_user.events
        .where(contract: @contract, status: 'approved')
        .where('event_type < ?', 5)
        .where(start_time: day_range)
        .first
    
    today_break = current_user.breaks
      #.includes(:daily_log) # Include daily_log to avoid n+1
      .where(contract: @contract)
      .where(start_time: day_range)
      .first
  
    today_activities = current_user.daily_activities
      .includes(:daily_log) # Include daily_log to avoid n+1
      .where(
        company: @company,
        contract: @contract,
        start_time: current_date.beginning_of_day..current_date.end_of_day
      )
      .order(created_at: :desc)
  
    settings = current_user.user_setting # Already eager loaded through user
  
    render json: {
      last_log: last_log,
      today_break: today_break,
      settings: settings,
      activities: today_activities,
      today_event: today_event,
      contract_id: @contract.id # Include contract ID for WebSocket updates
    }
  end

  # Get basic metrics for each contract
  def team_summary
    authorize! @company, to: :team_summary?

    
    summaries = @company.contracts.includes(:user).map do |contract|
      logs = contract.daily_logs.where(
        start_time: Date.current.beginning_of_month..Date.current.end_of_month
      )
      
      {
        employee_name: contract.first_name || contract.email,
        days_worked: logs.count,
        total_hours: logs.sum(:duration) / 3600.0,
        last_active: logs.maximum(:start_time)
      }
    end
  
    render json: summaries
  end


  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def daily_logs_t(key, **options)
    t("controllers.daily_logs.#{key}", **options)
  end

  def handle_previous_open_logs
    @notifications = []
    today = Time.current.to_date
    
    open_logs = @contract.daily_logs.where(end_time: nil)
    open_logs.each do |log|
      if log.start_time.to_date < today
        close_previous_log(log)
        handle_previous_day_break(log) if should_add_break?(log)

        @notifications << {
          type: 'warning',
          message: "Previous log from #{I18n.l(log.start_time.to_date)} was automatically closed with #{log.duration_in_text}"
        }
      end
    end
  end

  def close_previous_log(log)
    end_time = [
      log.start_time + 8.hours,
      log.start_time.end_of_day - 1.hour
    ].min
    log.update(end_time: end_time)
    
    # Use AutoCloser service to close orphaned activities
    DailyActivityManager::AutoCloser.call(daily_log: log, close_time: end_time)
  end

  def should_add_break?(log)
    # Only add break if:
    # 1. No break exists for that day
    # 2. Work duration was long enough (e.g., > 6 hours)
    # 3. Work period spans over configured break time
    work_date = log.start_time.to_date
    break_start = current_user.user_setting.break_start
    break_datetime = work_date.in_time_zone.change(
      hour: break_start.hour,
      min: break_start.min
    )
     
    # break_time = Time.parse(current_user.user_setting.break_start)
    # work_date = log.start_time.to_date
    # break_datetime = break_time.change(year: work_date.year, month: work_date.month, day: work_date.day)
    
    duration = (log.end_time - log.start_time).to_i
    !log.breaks.exists? &&
      duration > 6.hours.to_i &&
      log.start_time < break_datetime &&
      log.end_time > break_datetime
  end

  def handle_previous_day_break(log)
    work_date = log.start_time.to_date
    break_start = current_user.user_setting.break_start
    break_datetime = work_date.in_time_zone.change(
      hour: break_start.hour,
      min: break_start.min
    )
    
    # Calculate break end time and ensure it doesn't exceed daily log end time
    calculated_break_end = break_datetime + @company.company_setting.break_duration.minutes
    break_end_time = [calculated_break_end, log.end_time].min
    
    log.breaks.create!(
      user: current_user,
      contract: @contract,
      start_time: break_datetime,
      end_time: break_end_time
    )
  end

  def safe_parse_date(date_string)
    return nil unless date_string =~ /^\d{4}-\d{2}-\d{2}$/
    Date.parse(date_string)
  rescue ArgumentError
    nil
  end

  def set_contract
    @contract = current_user.contracts.active_only.find_by(company: @company)
    unless @contract
      redirect_to root_path, alert: daily_logs_t('errors.no_workspace_connection')
    end
  end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def daily_log_params
    params.require(:daily_log).permit(:description, :start_time, :end_time, :place)
  end
  
  def broadcast_team_status_update(employee:, daily_log:, event_type:)
    return unless @contract

    # Get employee name from contract
    employee_name = "#{@contract.first_name} #{@contract.last_name}"

    # Build the broadcast data matching the specification
    broadcast_data = {
      type: 'team_status_update',
      event_type: event_type,
      employee_id: @contract.id, # Use contract_id to match frontend expectations
      employee_name: employee_name,
      working: event_type == 'daily_log_started',
      daily_log_started_at: daily_log.start_time&.iso8601,
      daily_log_ended_at: daily_log.end_time&.iso8601
    }

    # Broadcast to the company channel (received by all employees and managers)
    TeamStatusChannel.broadcast_to(
      @company,
      broadcast_data
    )
    
    Rails.logger.info "[TeamStatusChannel] Broadcasting #{event_type} for employee #{employee.email} in company #{@company.name}"
  rescue => e
    Rails.logger.error "[TeamStatusChannel] Failed to broadcast: #{e.message}"
    # Don't let broadcast failures affect the main action
  end

end

