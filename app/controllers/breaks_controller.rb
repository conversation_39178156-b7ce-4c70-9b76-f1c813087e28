class BreaksController < ApplicationController
  
  # Authentication handled by ApplicationController's require_login
  before_action :set_tenant_company
  before_action :set_contract
  before_action :set_break, only: [:update]
  before_action :set_daily_log, only: [:create_for_report]


  def create
    settings = current_user.user_setting
    day_range = DateTimeHelper.day_range(Date.today)
    @daily_log = current_user.daily_logs.find_by(start_time: day_range)
    
    @break = @daily_log.breaks.new(
      start_time: Time.current,
      end_time: settings&.auto_break ? Time.current + settings.default_break_duration.minutes : nil
    )

    if @break.save
      render json: @break, status: :created
    else
      render json: { errors: @break.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def create_for_report
    date_string = params.dig(:break, :date)
  
    if date_string.present? && date_string =~ /^\d{4}-\d{2}-\d{2}$/
      begin
        date = Date.parse(date_string)
      rescue ArgumentError
        return render json: { error: breaks_t('errors.invalid_date_format') }, status: :unprocessable_entity
      end
    else
      return render json: { error: breaks_t('errors.date_required') }, status: :unprocessable_entity
    end
  
    @daily_log = current_user.daily_logs.find(params[:break][:daily_log_id])
    @break = @daily_log.breaks.build(
      user: current_user,
      contract: @contract,
      daily_log: @daily_log
    )
    
    zone = Time.zone
    default_start_hour = current_user.user_setting&.break_start&.hour || 12
    @break.start_time = date.in_time_zone(zone).change(hour: default_start_hour)
    puts "\n ---------- #{@break.inspect} ---------- \n\n"

    @break.end_time = @break.start_time + 30.minutes
    puts "\n ---------- #{@break.inspect} ---------- \n\n"
    
    if @break.save
      render json: @break, status: :created
    else
      puts "\n ---------- #{@break.errors.full_messages} ---------- \n\n"
      render json: { errors: @break.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @break.update(break_params)
      render json: @break
    else
      render json: { errors: @break.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # After Timezone refactor
  def today
    day_range = DateTimeHelper.day_range(Date.current)
    @break = current_user.breaks.find_by(start_time: day_range)

    render json: @break
  end


  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def breaks_t(key, **options)
    t("controllers.breaks.#{key}", **options)
  end

  def set_break
    @break = current_user.breaks.find(params[:id])
  end

  def set_daily_log
    @daily_log = current_user.daily_logs.find(params[:break][:daily_log_id])
  end

  def set_contract
    @contract = current_user.contracts.active_only.find_by(company: @company)
    unless @contract
      redirect_to root_path, alert: breaks_t('errors.no_workspace_connection')
    end
  end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def break_params
    params.require(:break).permit(:start_time, :end_time)
  end
end