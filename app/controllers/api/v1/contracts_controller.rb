# ABOUTME: API v1 controller for contract-related operations including colleague access
# ABOUTME: Implements secure JWT authentication and proper authorization policies
module Api
  module V1
    class ContractsController < ApiController
      before_action :set_contract
      
      # GET /api/v1/contracts/colleagues
      def colleagues
        # DEBUG: Log authentication state
        Rails.logger.info "[DEBUG] Api::V1::ContractsController#colleagues: Authentication check"
        Rails.logger.info "[DEBUG] - current_user: #{current_user&.id}"
        Rails.logger.info "[DEBUG] - @current_jwt_user: #{@current_jwt_user&.id}"
        Rails.logger.info "[DEBUG] - @company: #{@company&.id}"
        Rails.logger.info "[DEBUG] - Request format: #{request.format}"
        Rails.logger.info "[DEBUG] - Authorization header: #{request.headers['Authorization']&.first(50)}"
        
        # Verify policy - any authenticated company user can view colleagues
        authorize! @company, to: :view_colleagues?
        
        # Find the current user's contract ID within this company
        current_user_contract_id = @contract&.id
        
        # Check if we should include the current user (for work assignments)
        include_self = params[:include_self] == 'true'
        
        colleagues = @company.contracts
                            .where(status: :active)
                            .where.not(user_id: nil)
        
        # Exclude current user unless include_self is true
        colleagues = colleagues.where.not(id: current_user_contract_id) unless include_self
        
        colleagues = colleagues.select(:id, :first_name, :last_name, :email, :job_title)
                              .order(:last_name, :first_name)
        
        # Explicitly render only the selected attributes to avoid calling default as_json
        colleagues_data = colleagues.map { |c| 
          { 
            id: c.id, 
            first_name: c.first_name, 
            last_name: c.last_name, 
            email: c.email, 
            job_title: c.job_title,
            is_current_user: c.id == current_user_contract_id
          } 
        }
        
        render json: {
          colleagues: colleagues_data,
          current_user_contract_id: current_user_contract_id
        }
      end
      
      private
      
      def set_contract
        @contract = current_user.contracts.active_only.find_by(company: @company)
        unless @contract
          render json: { error: I18n.t('controllers.works.errors.no_workspace_connection') }, status: :forbidden
        end
      end
    end
  end
end