module Api
  module V1
    class WorksController < ApiController
      before_action :set_contract
      before_action :set_work, only: [:update, :reschedule, :destroy, :force_close_activities]
      
      def index
        # Returns all works for the company
        authorize! @company, to: :view_works?
        
        if @contract
          works = @company.works
            .order(created_at: :desc)
            .includes(:work_assignments)
          
          works_json = works.as_json(include: { 
            work_assignments: { 
              include: { contract: { only: [:id, :first_name, :last_name] } } 
            } 
          })
          
          render json: works_json
        else
          Rails.logger.warn "[🔧 ERROR] No contract found for user #{current_user&.email} in company #{@company&.id}"
          render json: []
        end
      end
      def assigned
        # Returns only works assigned to current user, but shows all team members on each work
        authorize! @company, to: :view_works?

        if @contract
          puts "[🔧 DEBUG] Contract ID: #{@contract.id}"
          
          # Use ActiveRecord subquery - cleanest approach per Gemini recommendation
          assigned_work_ids = WorkAssignment.where(contract_id: @contract.id).select(:work_id)
          puts "[🔧 DEBUG] Assigned work IDs query: #{assigned_work_ids.to_sql}"
          
          works = @company.works
            .where(id: assigned_work_ids)
            .order(created_at: :desc)
            .includes(work_assignments: :contract)
          
          puts "[🔧 DEBUG] Final works query: #{works.to_sql}"
          puts "[🔧 DEBUG] Found #{works.count} works"
          
          # Debug each work's assignments
          works.each do |work|
            puts "[🔧 DEBUG] Work #{work.id} has #{work.work_assignments.count} assignments: #{work.work_assignments.pluck(:contract_id)}"
          end
          
          result = works.as_json(include: { 
            work_assignments: { 
              include: { contract: { only: [:id, :first_name, :last_name] } } 
            } 
          })
          
          puts "[🔧 DEBUG] JSON result work assignments counts: #{result.map { |w| [w['id'], w['work_assignments']&.length] }}"
          
          render json: result
        else
          render json: []
        end
      end

      def today
        # Returns only works assigned to current user starting today or later
        authorize! @company, to: :view_works?

        unless @contract
          return render json: []
        end

        today_date = Date.current
        assigned_work_ids = WorkAssignment.where(contract_id: @contract.id).select(:work_id)

        @works = @company.works
          .where(id: assigned_work_ids)
          .where('scheduled_start_date >= ?', today_date)
          .where(status: ['scheduled', 'in_progress', 'unprocessed'])
          .order(:scheduled_start_date, :created_at)
          .includes(work_assignments: :contract)

        render json: @works.as_json(include: {
          work_assignments: {
            include: { contract: { only: [:id, :first_name, :last_name] } }
          }
        })
      end
      
      # PATCH /api/v1/works/:id/reschedule
      def reschedule
        # Check authorization - user can only reschedule works they can manage
        unless can_reschedule_work?(@work)
          render json: { error: I18n.t('controllers.works.errors.unauthorized') }, status: :forbidden
          return
        end
        
        # Update the work dates
        if @work.update(reschedule_params)
          render json: { 
            success: true, 
            work: @work.as_json(include: { 
              work_assignments: { 
                include: { contract: { only: [:id, :first_name, :last_name] } } 
              } 
            }), 
            message: I18n.t('controllers.works.messages.rescheduled') 
          }
        else
          render json: { 
            success: false, 
            message: @work.errors.full_messages.first,
            errors: @work.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end
      
      def create
        authorize! @company, to: :manage_works?
        
        @work = @company.works.build(work_params)
        
        ActiveRecord::Base.transaction do
          # Handle ServiceContract creation or selection
          if params[:service_contract_id] == 'new' || params[:service_contract_id].blank?
            # Create new ServiceContract
            service_contract_title = params[:service_contract_title].present? ? 
              params[:service_contract_title] : 
              ServiceContract.generate_generic_title(@company.id)
              
            @service_contract = @company.service_contracts.build(
              title: service_contract_title,
              description: params[:service_contract_description],
              client_id: @work.client_id,
              status: 'scheduled'
            )
            
            if @service_contract.save
              @work.service_contract = @service_contract
            else
              render json: { success: false, errors: @service_contract.errors.full_messages }, status: :unprocessable_entity
              return
            end
          else
            # Use existing ServiceContract
            @service_contract = @company.service_contracts.find(params[:service_contract_id])
            @work.service_contract = @service_contract
          end
          
          if @work.save
            # Handle work assignments
            unless handle_work_assignments(@work)
              render json: { success: false, errors: @work.errors.full_messages }, status: :unprocessable_entity
              raise ActiveRecord::Rollback
            end
            
            render json: @work.as_json(include: { 
              service_contract: { only: [:id, :title] },
              work_assignments: { 
                include: { contract: { only: [:id, :first_name, :last_name] } } 
              } 
            }), status: :created
          else
            render json: { success: false, errors: @work.errors.full_messages }, status: :unprocessable_entity
          end
        end
      end
      
      def update
        authorize! @company, to: :manage_works?
        
        ActiveRecord::Base.transaction do
          # Handle ServiceContract change
          if params[:service_contract_id] == 'new'
            # Create new ServiceContract
            service_contract_title = params[:service_contract_title].present? ? 
              params[:service_contract_title] : 
              ServiceContract.generate_generic_title(@company.id)
              
            @service_contract = @company.service_contracts.build(
              title: service_contract_title,
              description: params[:service_contract_description],
              client_id: @work.client_id,
              status: 'scheduled'
            )
            
            if @service_contract.save
              @work.service_contract = @service_contract
            else
              render json: { success: false, errors: @service_contract.errors.full_messages }, status: :unprocessable_entity
              return
            end
          elsif params[:service_contract_id].present?
            # Change to existing ServiceContract
            @service_contract = @company.service_contracts.find(params[:service_contract_id])
            @work.service_contract = @service_contract
          end
          
          # Handle work assignments before updating work
          unless handle_work_assignments(@work, for_update: true)
            render json: { success: false, errors: @work.errors.full_messages }, status: :unprocessable_entity
            raise ActiveRecord::Rollback
          end
          
          if @work.update(work_params)
            render json: @work.as_json(include: { 
              service_contract: { only: [:id, :title] },
              work_assignments: { 
                include: { contract: { only: [:id, :first_name, :last_name] } } 
              } 
            })
          else
            render json: { success: false, errors: @work.errors.full_messages }, status: :unprocessable_entity
          end
        end
      end
      
      def destroy
        authorize! @company, to: :manage_works?
        
        if @work.destroy
          head :no_content
        else
          render json: { 
            success: false, 
            errors: @work.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end
      
      def force_close_activities
        authorize! @company, to: :manage_works?
        
        count = DailyActivity.force_close_work_activities(@work.id, "Force closed by manager")
        render json: { 
          success: true, 
          message: I18n.t('controllers.works.messages.force_close_activities', count: count), 
          count: count 
        }
      end
      
      private
      
      def set_contract
        @contract = current_user.contracts.active_only.find_by(company: @company)
      end
      
      def set_work
        @work = @company.works.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: I18n.t('controllers.works.errors.not_found') }, status: :not_found
      end
      
      def reschedule_params
        params.permit(:scheduled_start_date, :scheduled_end_date)
      end
      
      def work_params
        params.permit(:title, :description, :location, :status, :scheduled_start_date, :scheduled_end_date, 
                     :client_id, :work_type, :is_recurring, :latitude, :longitude, :duration, 
                     :preferred_period, :specific_time, :confirmed_time)
      end
      
      def can_reschedule_work?(work)
        # Use the proper authorization policy for work scheduling
        allowed_to?(:manage_work_scheduling?, @company)
      end
      
      def handle_work_assignments(work, for_update: false)
        if for_update
          handle_assignment_updates(work)
        else
          handle_assignment_creation(work)
        end
      end
      
      def handle_assignment_creation(work)
        if params[:assigned_contracts].present?
          valid_contract_ids = @company.contracts.where(id: params[:assigned_contracts]).pluck(:id)
          
          # Ensure at least one assignment
          if valid_contract_ids.empty?
            work.errors.add(:base, I18n.t('controllers.works.errors.at_least_one_assignment'))
            return false
          end
          
          # Create assignments
          valid_contract_ids.each_with_index do |contract_id, index|
            work.work_assignments.create!(
              contract_id: contract_id,
              company_id: @company.id,
              role: params[:assignment_role] || "worker",
              is_lead: index == 0 # First assignment is lead
            )
          end
        else
          # If no assignments provided, assign to creator by default
          work.work_assignments.create!(
            contract_id: @contract.id,
            company_id: @company.id,
            role: "worker",
            is_lead: true
          )
        end
        true
      rescue ActiveRecord::RecordInvalid => e
        work.errors.add(:base, e.message)
        false
      end
      
      def handle_assignment_updates(work)
        return true unless params.key?(:assigned_contracts)
        
        requested_ids = params[:assigned_contracts] || []
        
        if requested_ids.present?
          valid_contract_ids = @company.contracts.where(id: requested_ids).pluck(:id)
          
          if valid_contract_ids.empty?
            work.errors.add(:base, I18n.t('controllers.works.errors.at_least_one_assignment'))
            return false
          end
          
          # Remove assignments not in new list
          work.work_assignments.where.not(contract_id: valid_contract_ids).destroy_all
          
          # Add new assignments
          existing_contract_ids = work.work_assignments.pluck(:contract_id)
          new_contract_ids = valid_contract_ids - existing_contract_ids
          
          new_contract_ids.each_with_index do |contract_id, index|
            work.work_assignments.create!(
              contract_id: contract_id,
              company_id: @company.id,
              role: params[:assignment_role] || "worker",
              is_lead: work.work_assignments.empty? && index == 0
            )
          end
          
          # Ensure at least one lead exists
          unless work.work_assignments.exists?(is_lead: true)
            work.work_assignments.first&.update!(is_lead: true)
          end
        else
          # Remove all assignments and assign to current user
          work.work_assignments.destroy_all
          work.work_assignments.create!(
            contract_id: @contract.id,
            company_id: @company.id,
            role: "worker",
            is_lead: true
          )
        end
        true
      rescue ActiveRecord::RecordInvalid => e
        work.errors.add(:base, e.message)
        false
      end
    end
  end
end