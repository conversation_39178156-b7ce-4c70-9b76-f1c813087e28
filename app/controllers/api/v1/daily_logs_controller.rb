module Api
  module V1
    class DailyLogsController < ApiController
      include <PERSON>F<PERSON>cher
      
      # Authentication is handled by <PERSON>piControll<PERSON> via authenticate_api_request!
      # Tenant context is handled by ApiController via establish_and_require_tenant_context
      # @company is set by ApiController's set_tenant_company
      before_action :set_contract
      
      # TODO: Consider adding explicit authorize! calls for specific resource instances where appropriate.
      # This aligns with the broader authorization strategy (Chunk 29).
      # See: docs/jwt_implementation_notes_chunk_19_final.md - "Potential Future Enhancements #4"
      before_action :set_daily_log, only: [:update, :destroy]

      # GET /api/v1/daily_logs
      def index
        daily_logs = @contract.daily_logs.order(created_at: :desc)
        render json: daily_logs
      end

      # GET /api/v1/daily_logs/fetch
      # Fetch daily logs for a specific date
      def fetch
        start_of_day = DateTimeHelper.beginning_of_day(Date.parse(params[:date]))
        end_of_day = DateTimeHelper.end_of_day(start_of_day)

        daily_logs = @contract.daily_logs
                              .where(start_time: start_of_day..end_of_day)
                              .includes(:daily_activities, :breaks)
                              .as_json(include: [:daily_activities, :breaks])

        render json: { daily_logs: daily_logs }
      rescue Date::Error
        render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
      end

      # GET /api/v1/daily_logs/last
      # Get the last daily log with activities
      def last
        last_log = @contract.daily_logs.includes(:daily_activities).last
        if last_log
          render json: last_log.as_json(include: :daily_activities)
        else
          render json: nil
        end
      end

      # GET /api/v1/daily_logs/fetch_report_data
      # Fetch report data for a month
      def fetch_report_data
        year = params[:year].to_i
        month = params[:month].to_i
        start_date = Date.new(year, month, 1)
        end_date = start_date.end_of_month

        daily_logs = @contract.daily_logs
                              .where('DATE(start_time) BETWEEN ? AND ?', start_date, end_date)
                              .includes(:daily_activities, :breaks)

        holidays = fetch_holidays(year, month)

        render json: { 
          daily_logs: daily_logs.as_json(include: [:daily_activities, :breaks]),
          holidays: holidays
        }
      end

      # GET /api/v1/daily_logs/current_status
      # Get current work status
      def current_status
        today = Date.current
        day_range = DateTimeHelper.day_range(today)
        
        current_log = @contract.daily_logs.find_by(start_time: day_range, end_time: nil)
        
        if current_log
          active_activity = current_log.daily_activities.where(end_time: nil).last
          render json: {
            is_working: true,
            daily_log: current_log,
            active_activity: active_activity
          }
        else
          render json: {
            is_working: false,
            daily_log: nil,
            active_activity: nil
          }
        end
      end

      # POST /api/v1/daily_logs
      def create
        handle_previous_open_logs
        today = Time.current.to_date
        start_time = Time.current
        day_range = DateTimeHelper.day_range(today)
        daily_log = @contract.daily_logs.find_by(start_time: day_range)

        unless daily_log
          daily_log = @contract.daily_logs.create(start_time: start_time, user: current_user)
        end

        notifications = []
        if daily_log.end_time.present?
          daily_log.reopen
          notifications << {
            type: 'info',
            message: t('controllers.daily_logs.messages.continuing_todays_log')
          }
        end
        
        if daily_log.valid?
          notifications << daily_log.time_warning if daily_log.time_warning.present?
          
          # Broadcast team status update when employee starts work
          broadcast_team_status_update(
            employee: current_user,
            daily_log: daily_log,
            event_type: 'daily_log_started'
          )
          
          render json: { daily_log: daily_log, notifications: notifications }
        else
          render json: { errors: daily_log.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # PATCH/PUT /api/v1/daily_logs/:id
      def update
        if @daily_log.update(daily_log_params)
          # If ending the daily log, also end all active activities
          if daily_log_params[:end_time].present?
            end_active_activities(@daily_log)
            
            # Broadcast team status update when employee ends work
            broadcast_team_status_update(
              employee: current_user,
              daily_log: @daily_log,
              event_type: 'daily_log_ended'
            )
          end
          
          notifications = []
          notifications << @daily_log.time_warning if @daily_log.time_warning.present?
          render json: { daily_log: @daily_log, notifications: notifications }
        else
          render json: { errors: @daily_log.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # DELETE /api/v1/daily_logs/:id
      def destroy
        if @daily_log.destroy
          render json: { message: t('controllers.daily_logs.messages.deleted') }, status: :ok
        else
          render json: { errors: @daily_log.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # POST /api/v1/daily_logs/create_for_report
      def create_for_report
        date = Date.parse(params[:date])
        start_time = DateTime.parse("#{date} #{params[:start_time]}")
        end_time = DateTime.parse("#{date} #{params[:end_time]}")

        daily_log = @contract.daily_logs.new(
          start_time: start_time,
          end_time: end_time,
          user: current_user
        )

        if daily_log.save
          # Create initial activity if description provided
          if params[:description].present?
            daily_log.daily_activities.create(
              start_time: start_time,
              end_time: end_time,
              description: params[:description]
            )
          end
          render json: { daily_log: daily_log.as_json(include: :daily_activities) }
        else
          render json: { errors: daily_log.errors.full_messages }, status: :unprocessable_entity
        end
      rescue ArgumentError, Date::Error
        render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
      end

      # GET /api/v1/daily_logs/team_summary
      def team_summary
        if can_view_team_summary?
          date = params[:date] ? Date.parse(params[:date]) : Date.current
          day_range = DateTimeHelper.day_range(date)
          
          team_logs = @company.daily_logs
                              .includes(:contract, :daily_activities, :breaks)
                              .where(start_time: day_range)
                              .map do |log|
            {
              id: log.id,
              contract: {
                id: log.contract.id,
                first_name: log.contract.first_name,
                last_name: log.contract.last_name
              },
              start_time: log.start_time,
              end_time: log.end_time,
              total_duration: log.total_duration,
              daily_activities: log.daily_activities,
              breaks: log.breaks
            }
          end
          
          render json: { team_logs: team_logs }
        else
          render json: { error: t('controllers.daily_logs.errors.unauthorized') }, status: :forbidden
        end
      rescue ArgumentError, Date::Error
        render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
      end

      # GET /api/v1/daily_logs/fetch_employee_report_data
      # Fetch report data for a specific employee (for owners/managers)
      def fetch_employee_report_data
        # Check authorization - only owners/managers can view employee reports
        unless can_view_team_summary?
          return render json: { error: t('controllers.daily_logs.errors.unauthorized') }, status: :forbidden
        end

        contract_id = params[:contract_id]
        year = params[:year].to_i
        month = params[:month].to_i
        
        unless contract_id.present?
          return render json: { error: t('controllers.daily_logs.errors.contract_id_required') }, status: :unprocessable_entity
        end

        # Find the employee's contract within the company
        employee_contract = @company.contracts.find_by(id: contract_id)
        unless employee_contract
          return render json: { error: t('controllers.daily_logs.errors.employee_not_found') }, status: :not_found
        end

        start_date = Date.new(year, month, 1)
        end_date = start_date.end_of_month

        daily_logs = employee_contract.daily_logs
                                      .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
                                      .includes(:daily_activities, :breaks)

        holidays = fetch_holidays(year, month)
        
        events = employee_contract.events.where(start_time: start_date.beginning_of_day..end_date.end_of_day)

        render json: { 
          daily_logs: daily_logs.as_json(include: [:daily_activities, :breaks]),
          holidays: holidays,
          events: events,
          employee: {
            id: employee_contract.id,
            first_name: employee_contract.first_name,
            last_name: employee_contract.last_name,
            job_title: employee_contract.job_title
          }
        }
      rescue Date::Error
        render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
      end

      # GET /api/v1/daily_logs/owner_work_summary
      # Get company-wide monthly work summary for owners/managers
      def owner_work_summary
        unless can_view_team_summary?
          return render json: { error: t('controllers.daily_logs.errors.unauthorized') }, status: :forbidden
        end

        year = params[:year]&.to_i || Date.current.year
        month = params[:month]&.to_i || Date.current.month
        
        # Validate year and month
        unless validate_date_params(year, month)
          return render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
        end

        summary_data = OwnerWorkSummaryService.generate_summary(@company, year, month)
        
        render json: { 
          summary: summary_data,
          selected_date: { year: year, month: month },
          company_name: @company.name
        }
      rescue => e
        Rails.logger.error "OwnerWorkSummary error: #{e.message}"
        render json: { error: t('controllers.daily_logs.errors.summary_generation_failed') }, status: :internal_server_error
      end

      # GET /api/v1/daily_logs/service_contract_summary
      # Get company-wide service contract summary (overall by default, monthly if year/month provided)
      def service_contract_summary
        unless can_view_team_summary?
          return render json: { error: t('controllers.daily_logs.errors.unauthorized') }, status: :forbidden
        end

        # Support both overall and monthly views
        year = params[:year]&.to_i
        month = params[:month]&.to_i
        
        # Validate year and month if provided (for monthly view)
        if year && month && !validate_date_params(year, month)
          return render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
        end

        # Generate summary (overall if no year/month, monthly if provided)
        summary_data = ServiceContractSummaryService.generate_summary(@company, year, month)
        
        response_data = { 
          service_contracts: summary_data[:service_contracts],
          summary_stats: summary_data[:summary_stats],
          company_name: @company.name
        }
        
        # Include selected_date only for monthly views
        if year && month
          response_data[:selected_date] = { year: year, month: month }
        end
        
        render json: response_data
      rescue => e
        Rails.logger.error "ServiceContractSummary error: #{e.message}"
        render json: { error: t('controllers.daily_logs.errors.summary_generation_failed') }, status: :internal_server_error
      end

      # GET /api/v1/daily_logs/works_summary
      # Get company-wide monthly works summary for owners/managers
      def works_summary
        unless can_view_team_summary?
          return render json: { error: t('controllers.daily_logs.errors.unauthorized') }, status: :forbidden
        end

        year = params[:year]&.to_i || Date.current.year
        month = params[:month]&.to_i || Date.current.month
        
        # Validate year and month
        unless validate_date_params(year, month)
          return render json: { error: t('controllers.daily_logs.errors.invalid_date') }, status: :unprocessable_entity
        end

        summary_data = WorksSummaryService.generate_summary(@company, year, month)
        
        render json: { 
          works: summary_data[:works],
          summary_stats: summary_data[:summary_stats],
          selected_date: { year: year, month: month },
          company_name: @company.name
        }
      rescue => e
        Rails.logger.error "WorksSummary error: #{e.message}"
        render json: { error: t('controllers.daily_logs.errors.summary_generation_failed') }, status: :internal_server_error
      end

      private

      def validate_date_params(year, month, allow_nil: false)
        return true if allow_nil && year.nil? && month.nil?
        return false if year.nil? || month.nil?
        
        year >= 2000 && year <= Date.current.year + 1 && month >= 1 && month <= 12
      end

      def set_contract
        @contract = current_user.contracts.active_only.find_by(company: @company)
        unless @contract
          render json: { error: t('controllers.daily_logs.errors.no_contract') }, status: :forbidden
        end
      end


      def set_daily_log
        @daily_log = @contract.daily_logs.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: t('controllers.daily_logs.errors.not_found') }, status: :not_found
      end

      def daily_log_params
        params.require(:daily_log).permit(:start_time, :end_time)
      end

      def handle_previous_open_logs
        @contract.daily_logs.where(end_time: nil).where.not(start_time: DateTimeHelper.day_range(Date.current)).find_each do |log|
          log.update(end_time: log.start_time.end_of_day)
          end_active_activities(log)
        end
      end

      def end_active_activities(daily_log)
        daily_log.daily_activities.where(end_time: nil).update_all(end_time: daily_log.end_time)
      end

      def can_view_team_summary?
        current_user.company_user_roles.where(company: @company).joins(:role)
                    .where(roles: { name: ['owner', 'admin', 'supervisor'] }).exists?
      end

      def broadcast_team_status_update(employee:, daily_log:, event_type:)
        # Get current work activity if employee just started working
        current_activity = nil
        current_work = nil
        
        if event_type == 'daily_log_started'
          current_activity = daily_log.daily_activities
            .where(end_time: nil, activity_type: DailyActivity::WORK_ACTIVITY_TYPES)
            .includes(:work)
            .first
          current_work = current_activity&.work
        end

        # Get employee name from contract
        employee_name = if @contract
          "#{@contract.first_name} #{@contract.last_name}"
        else
          employee.email
        end

        # Build the broadcast data
        broadcast_data = {
          type: 'team_status_update',
          event_type: event_type,
          employee_id: @contract.id, # Use contract_id to match frontend expectations
          employee_name: employee_name,
          working: event_type == 'daily_log_started',
          daily_log_started_at: daily_log.start_time&.iso8601,
          daily_log_ended_at: daily_log.end_time&.iso8601
        }

        # Include current work details if available
        if current_work
          broadcast_data[:current_work] = {
            id: current_work.id,
            title: current_work.title,
            location: current_work.location.presence || current_work.address
          }
          broadcast_data[:activity_started_at] = current_activity.start_time&.iso8601
          broadcast_data[:activity_type] = current_activity.activity_type
        end

        # Broadcast to the company channel (received by all managers)
        TeamStatusChannel.broadcast_to(
          @company,
          broadcast_data
        )
        
        Rails.logger.info "[TeamStatusChannel] Broadcasting #{event_type} for employee #{employee.email} in company #{@company.name}"
      rescue => e
        Rails.logger.error "[TeamStatusChannel] Failed to broadcast: #{e.message}"
        # Don't let broadcast failures affect the main action
      end
    end
  end
end