class ReportsController < ApplicationController
  # Authentication handled by ApplicationController's require_login
  before_action :set_tenant_company
  before_action :set_contract

  def activities
    # View for the activities dashboard
  end
  
  #TODO: check authorization
  def send_pdf
    recipient_email = params[:report_recipient]
    unless recipient_email.present? && recipient_email =~ /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
      return render json: { error: reports_t('errors.invalid_email') }, status: :unprocessable_entity
    end

    selected_date = params[:selected_date]
    
    report_data = ReportDataService.generate_report_data(@contract, selected_date)
    
    report_data[:recipient_email] = recipient_email
    report_data[:company_name] = params[:company_name] if params[:company_name].present?
    report_data[:employee_name] = params[:employee_name] if params[:employee_name].present?
    report_data[:include_summary_fields] = params[:include_summary_fields]
    report_data[:days_worked] = params[:days_worked]
    report_data[:event_counts] = params[:event_counts]
    formatted_hours = format_duration(report_data[:total_hours])
    formatted_month = format_month_year(selected_date)
    puts "---------- formatted_month: #{formatted_month}"
    
    begin
      pdf_data, filename = PdfGenerator.attendance_report(report_data)
      
      ReportMailer.attendance_report(
        recipient_email,
        pdf_data,
        filename,
        report_data[:employee_name],
        formatted_month,
        formatted_hours,
      ).deliver_now
      
      render json: { message: reports_t('messages.report_sent') }, status: :ok
    rescue StandardError => e
      Rails.logger.error("Error generating PDF: #{e.message}\n#{e.backtrace.join("\n")}")
      render json: { error: reports_t('errors.generation_failed', message: e.message) }, status: :unprocessable_entity
    end
  end

  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def reports_t(key, **options)
    t("controllers.reports.#{key}", **options)
  end

  def format_month_year(date)
    date_obj = date.is_a?(Date) ? date : Date.parse(date.to_s)
    I18n.l(date_obj, format: "%B %Y", locale: I18n.locale).capitalize
  end

  def format_duration(seconds)
    return "" unless seconds
    (seconds.to_f / 3600).round(1).to_s
  end

  def set_contract
    @contract = current_user.contracts.active_only.find_by(company: @company)
    unless @contract
      redirect_to root_path, alert: reports_t('errors.no_workspace_connection')
    end
  end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end
end