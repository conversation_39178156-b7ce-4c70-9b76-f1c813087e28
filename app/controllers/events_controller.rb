class EventsController < ApplicationController
  include HolidayFetcher

  # Authentication handled by ApplicationController's require_login
  before_action :set_tenant_company
  before_action :set_contract

  # Events for the User and his Contracts
  def index
    # Generates index file with vue component only
    @contract_debug = @contract
  end

  # Gets the data for the index vue component
  # Tenant is set to company, Contract is a current_users first contract
  def fetch
    # This must be contract of current_user
    begin
      date_param = Date.parse(params[:date])
      if date_param
        date_param = safe_parse_date(params[:date])
        year = date_param.year
        month = date_param.month
        start_date = Date.new(year, month, 1)
        end_date = start_date.end_of_month+1 # 1 end_of_month ends at 00:00 for some reaosn not taking the whole day
        holidays = fetch_holidays(year, month)

        events = @contract.events.includes(:contract)
                          .where(start_time: start_date..end_date)
                          .as_json(include: {
                            contract: { only: [:id, :first_name, :last_name] }
                          })

        works = @company.works.where('scheduled_start_date BETWEEN ? AND ? OR scheduled_end_date BETWEEN ? AND ?', 
                          start_date, end_date, start_date, end_date)
                          .includes(:work_assignments, :booking)
                          .as_json(include: { 
                            work_assignments: { 
                              include: { contract: { only: [:id, :first_name, :last_name] } } 
                            },
                            booking: { only: [:id, :status, :client_name, :preferred_date, :preferred_period, :specific_time] }
                          })

        # Load meetings only for meeting users and managers
        meetings = if current_user.company_user_roles.where(company: @company).joins(:role)
                      .where(roles: { name: ['owner', 'admin', 'supervisor'] }).exists?
          # Managers can see all meetings
          @company.meetings.where('confirmed_date BETWEEN ? AND ?',
                                start_date, end_date)
                          .order(:confirmed_date)
        else
          # Regular users can only see meetings they're part of
          @company.meetings.joins(:meeting_users)
                          .where(meeting_users: { user_id: current_user.id })
                          .where('confirmed_date BETWEEN ? AND ?',
                                start_date, end_date)
                          .order(:confirmed_date)
        end

        meetings_json = meetings.as_json(include: { 
          meeting_users: { only: [:id, :email, :selected_dates] },
          created_by: { only: [:id, :email] }
        })
                           
        render json: { 
          events: events, 
          works: works, 
          meetings: meetings_json, 
          holidays: holidays 
        }
      else
        render json: { error: events_t('errors.invalid_date_format') }, status: :unprocessable_entity
      end
    rescue Date::Error
      render json: { error: events_t('errors.invalid_date_format') }, status: :unprocessable_entity
    end
  end

  # def show
  # end

  def create
    # Must be a contract of current user
    event = @contract.events.new(event_params)
    event.user_id = current_user.id
    if event.save
      # Include contract information in response so frontend shows names immediately
      event_with_contract = event.as_json(include: { 
        contract: { only: [:id, :first_name, :last_name] } 
      })
      render json: { success: true, event: event_with_contract, message: events_t('messages.created') }, status: :created
    else
      render json: { message: event.errors.full_messages.join(', '), messageType: 'error' }, status: :unprocessable_entity
    end
  end

  # def update
  #   @event = Event.find(params[:id])
  #   if @event.update(event_params)
  #     render json: @event
  #   else
  #     render json: @event.errors, status: :unprocessable_entity
  #   end
  # end

  # Vuejs endpoint. DELETE /events/1
  def destroy
    @event = Event.find(params[:id])
    @event.destroy
    head :no_content
  end

  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def events_t(key, **options)
    t("controllers.events.#{key}", **options)
  end

  def safe_parse_date(date_string)
    return nil unless date_string =~ /^\d{4}-\d{2}-\d{2}$/
    Date.parse(date_string)
  rescue ArgumentError
    nil
  end

  def set_contract
    @contract = current_user.contracts.active_only.find_by(company: @company)
    unless @contract
      redirect_to root_path, alert: events_t('errors.no_workspace_connection')
    end
  end
  
  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def event_params
    params.require(:event).permit(:event_type, :title, :start_time, :end_time, :description, :place)
  end
end