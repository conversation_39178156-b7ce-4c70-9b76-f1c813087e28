class Contract < ApplicationRecord
  acts_as_tenant(:company)
  
  enum status: { active: 0, suspended: 1, terminated: 2 }

  belongs_to :company, counter_cache: true
  belongs_to :user, optional: true

  # Prevent deletion if associated records exist to preserve audit trail
  has_many :daily_logs, dependent: :restrict_with_error
  has_many :daily_activities, dependent: :restrict_with_error
  has_many :events, dependent: :restrict_with_error
  has_many :breaks, dependent: :restrict_with_error
  has_many :work_assignments, dependent: :restrict_with_error
  has_many :works, through: :work_assignments
  has_many :meeting_users, dependent: :restrict_with_error

  validates :first_name, presence: true
  validates :company, presence: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }, if: -> { email.present? }
  validates :email, uniqueness: { 
    scope: :company_id,
    conditions: -> { where.not(status: :terminated) }
  }, if: -> { email.present? }
  
  # Prevent multiple active contracts per user-company pair
  validates :user_id, uniqueness: { 
    scope: [:company_id],
    conditions: -> { where(status: :active) },
    message: "can only have one active contract per company"
  }, if: -> { user_id.present? && active? }

  after_create :send_invitation, if: :email_present?

  scope :without_tenant, -> { unscope(where: :company_id) }
  scope :active_only, -> { where(status: :active) }
  scope :suspended_only, -> { where(status: :suspended) }
  scope :terminated_only, -> { where(status: :terminated) }

  #before_destroy :handle_company_user_role
  
  def translated_status
    I18n.t("statuses.#{status}")
  end
   
  def as_json(options = {})
    super(options).merge(
      translated_status: translated_status,
      terminated_at: terminated_at
    )
  end

  def invitation_sent_at
    User.find_by(email: self.email)&.invitation_sent_at
  end
  
  def invitation_accepted
    User.find_by(email: self.email)&.invitation_accepted_at.present?
  end

  def suspend!
    return false if terminated?
    
    begin
      transaction do
        update!(status: :suspended)
        handle_user_role_for_suspension
      end
      true
    rescue => e
      errors.add(:base, e.message)
      Rails.logger.error("Failed to suspend contract: #{e.message}")
      false
    end 
  end
  
  def reactivate!
    return false if terminated?
    
    begin
      transaction do
        update!(status: :active, terminated_at: nil)
        handle_user_role_for_reactivation
      end
      true
    rescue => e
      errors.add(:base, e.message)
      Rails.logger.error("Failed to reactivate contract: #{e.message}")
      false
    end
  end
  
  def terminate!
    begin
      transaction do
        update!(status: :terminated, terminated_at: Time.current)
        handle_user_role_for_termination
      end
      true
    rescue => e
      errors.add(:base, e.message)
      Rails.logger.error("Failed to terminate contract: #{e.message}")
      false
    end
  end

  
  private

  def handle_user_role_for_suspension
    return unless user.present?
    puts "\n -------- handle_user_role_for_suspension \n\n "

    company_user_role = CompanyUserRole.with_inactive.find_by(user: user, company: company)
    puts "\n -------- company_user_role: #{company_user_role.inspect} \n\n "

    if company_user_role
      begin
        # Use update! to raise errors instead of silently failing
        company_user_role.update!(active: false)
      rescue => e
        raise "Failed to update role: #{e.message}"
      end
    end

  end

  def handle_user_role_for_reactivation
    return unless user.present?
    
    # Use unscoped to find even inactive roles
    company_user_role = CompanyUserRole.with_inactive.find_by(user: user, company: company)
    
    if company_user_role
      begin
        # Use update! to raise errors
        company_user_role.update!(active: true)
      rescue => e
        raise "Failed to update role: #{e.message}"
      end
    end
  end

  def handle_user_role_for_termination
    return unless user.present?
    
    # Use with_inactive to find even inactive roles
    role = CompanyUserRole.with_inactive.find_by(user: user, company: company)
    puts "\n -------- role: #{role.inspect} \n\n "
    if role
      begin
        # Instead of trying to set user_id to nil, mark it as inactive
        role.update!(active: false)
      rescue => e
        raise "Failed to update role: #{e.message}"
      end
    end
  end

  # Not using yet. Handled in controller for now to manage owner or other roles
  # def handle_company_user_role
  #   company_user_role = CompanyUserRole.find_by(user: user, company: company)
  #   company_user_role&.destroy if company_user_role&.role&.name != 'owner'
  # end

  #new invitation model
  def email_present?
    email.present?
  end

  def send_invitation
    #TODO:  Change to current_user?
    owner = company.company_user_roles.joins(:role).find_by(roles: { name: 'owner' })&.user

    # CORE FIX: Use database-backed InvitationService instead of Redis-backed InvitationHandler
    # This ensures invitation tokens are stored in database where API validation can find them
    InvitationService.send_invitation(
      email: email,
      first_name: first_name,
      last_name: last_name,
      sender: owner,
      company: company
    )
  end

  
end
