# app/policies/daily_log_policy.rb
class DailyLogPolicy < TenantScopedPolicy
  # Action Policy scoping
  scope_for :index do |scope|
    # ActsAsTenant should already scope this, but we can be explicit
    scope.joins(:contract).where(contracts: { company_id: ActsAsTenant.current_tenant&.id })
  end
  
  def index?
    # User must have a contract in the current tenant
    user_has_contract_in_tenant?
  end
  
  def show?
    # Must belong to current tenant and user must have access
    tenant_authorized? && user_can_access_log?
  end
  
  def create?
    # User must have an active contract in the current tenant
    user_has_contract_in_tenant?
  end
  
  def update?
    # Must belong to current tenant and be the user's own log
    tenant_authorized? && user_owns_log?
  end
  
  def destroy?
    # Same as update
    update?
  end
  
  def fetch?
    index?
  end
  
  def current_status?
    index?
  end
  
  def team_summary?
    # Only supervisory roles can view team summary
    user_has_supervisory_role?
  end
  
  private
  
  def user_has_contract_in_tenant?
    return false unless ActsAsTenant.current_tenant
    
    user.contracts.active_only.where(company: ActsAsTenant.current_tenant).exists?
  end
  
  def user_can_access_log?
    # User can access their own logs or if they have supervisory role
    user_owns_log? || user_has_supervisory_role?
  end
  
  def user_owns_log?
    record.user_id == user.id || record.contract&.user_id == user.id
  end
  
  def user_has_supervisory_role?
    return false unless ActsAsTenant.current_tenant
    
    user.company_user_roles
        .joins(:role)
        .where(company: ActsAsTenant.current_tenant)
        .where(roles: { name: ['owner', 'admin', 'supervisor'] })
        .exists?
  end
end