class WorkPolicy < ApplicationPolicy
  # Get the company from the work record
  def company
    record.company
  end
  
  # Optimize role checking - caches roles to reduce DB queries
  def user_roles_in_company
    @user_roles ||= user.company_user_roles.joins(:role)
                         .where(company: company)
                         .pluck('roles.name')
  end

  def has_any_role?(*roles)
    roles.any? { |role| user_roles_in_company.include?(role) }
  end

  # Any user with a contract at the company can access works (read-only for employees)
  def access_works?
    has_any_role?("owner", "employee", "supervisor", "admin") 
  end

  # Read-only access to works for all roles
  def view_works?
    has_any_role?("owner", "employee", "supervisor", "admin")
  end

  def show?
    view_works?
  end

  # Management restricted to owners, supervisors, and admins (NOT employees)
  def manage_works?
    has_any_role?("owner", "supervisor", "admin")
  end
  
  # Prepare for future assignment-based permissions
  # Currently permissive, but structured for future assignment-based restrictions
  def manage_assigned_work?
    manage_works? || 
    (record.work_assignments.exists?(contract: user.contracts.active_only.where(company: company)))
  end

  def create?
    manage_works?
  end

  # In future, could change to: manage_works? || manage_assigned_work?
  def update?
    manage_works? 
  end

  # In future, could change to: manage_works? || manage_assigned_work?
  def destroy?
    manage_works?
  end
  
  # Template for paid tier feature check
  # This would normally be restricted to premium plans, but is currently fully accessible
  # In future: premium_plan? && manage_works?
  def can_create_recurring_works?
    manage_works?
  end

  # Check if the company has a plus plan
  def plus_plan?
    %w[premium plus].include?(company.current_plan&.name)
  end

  # Allow taking assignment if user has access to works
  def take_assignment?
    access_works?
  end

  # Allow leaving assignment if user has access to works
  def leave_assignment?
    access_works?
  end
  
end